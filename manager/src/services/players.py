from datetime import datetime, time
from zoneinfo import ZoneInfo
from pydantic import BaseModel
from typing import List

from beanie import BulkWriter
from beanie.operators import Or, In, NotIn

from src.db.models import Player
from src.schemas.requests import (
    PlayerUpdateRequest,
    AddMultipleUserAccountsRequest,
    RegisterPlayerRequest,
)
from src.schemas.responses import (
    PlayerBalanceResponse,
    AddUserAccountResponse,
    PlayerResponseExtended,
    PlayerResponseFiltered,
)
from src.services.player_registration_service import PlayerRegistrationService
from src.utils.enums import AppType, BotType, PlayerStatus, CurrencyType
from src.services.ip_pool_service import IPPoolConfigurationService
from src.utils.logging import logger

USD_GOLD_CONVERSION_RATE = 8.0


class PlayerToUpdate(BaseModel):
    player_id: str
    status: str | None = None
    enabled: bool | None = None

    bot_id: str | None = None
    table_id: str | None = None
    should_stop: bool | None = None
    bot_type: str | None = None
    last_error: str | None = None
    balance: PlayerBalanceResponse | None = None
    need_balance_update: bool | None = None

    hands_played: int | None = None
    total_buy_in: int | None = None
    last_buy_in: int | None = None
    rebuy_count: int | None = None
    stack: float | None = None

    chips: float | None = None
    rank: int | None = None
    strategy_profile: str | None = None

    updated_at: datetime | None = None


class PlayerService:
    @staticmethod
    async def get_players(
        app_id: int = None,
        club_id: int = None,
        only_active: bool = False,
        only_enabled: bool = True,
    ) -> List[PlayerResponseExtended]:
        if app_id is None:
            db_players = Player.all()
        else:
            db_players = Player.find(
                Or(Player.app_id == app_id, In(Player.allowed_games, [app_id]))
            )
        if club_id:
            db_players = db_players.find(In(Player.club_ids, [club_id]))
        if only_enabled:
            db_players = db_players.find(Player.enabled == True)  # noqa: E712
        if only_active:
            db_players = db_players.find(Player.status != PlayerStatus.IDLE.value)  # noqa: E712

        players = await PlayerService.get_players_extended(await db_players.to_list())
        return players

    @staticmethod
    async def get_filtered_players(
        page: int,
        page_size: int,
        sort: str,
        query: List = [],
    ) -> PlayerResponseFiltered:
        # Query
        players = (
            await Player.find(*query)
            .sort(sort)
            .skip((page - 1) * page_size)
            .limit(page_size)
            .to_list()
        )
        total_count = await Player.find(*query).count()
        return PlayerResponseFiltered(
            total_count=total_count,
            page=page,
            page_size=page_size,
            players=await PlayerService.get_players_extended(players),
        )

    @staticmethod
    async def get_players_extended(players: List[Player]) -> List[PlayerResponseExtended]:
        result: List[PlayerResponseExtended] = []
        player_ids = [p.player_id for p in players]
        matched_confs = await IPPoolConfigurationService.get_multiple_configurations_for_players(
            player_ids
        )
        for player in players:
            proxy_conf = next(
                (conf for conf in matched_confs if player.player_id in conf.assigned_player_ids),
                None,
            )
            external_ip = proxy_conf.external_ip if proxy_conf else None
            ip_conf_id = proxy_conf.ip_conf_id if proxy_conf else None
            player_response = PlayerResponseExtended(
                **player.to_response().model_dump(), external_ip=external_ip, ip_conf_id=ip_conf_id
            )
            result.append(player_response)

        return result

    @staticmethod
    async def update_player(player_id: str, player_data: PlayerUpdateRequest) -> Player:
        player = await Player.find_one(Player.player_id == player_id)
        if not player:
            raise ValueError("Player not found")

        update_data = player_data.model_dump(exclude_none=True)

        await player.set(update_data)

        return player

    @staticmethod
    async def get_all_scan_players() -> List[Player]:
        return await Player.find(Player.bot_type == BotType.SCAN.value).to_list()

    @staticmethod
    async def update_players(player_updates: List[PlayerToUpdate]):
        player_ids = [p.player_id for p in player_updates]
        players = await Player.find(In(Player.player_id, player_ids)).to_list()

        async with BulkWriter() as bulk_writer:
            for player_update in player_updates:
                update_object = player_update.model_dump(exclude_unset=True)
                if update_object.get("updated_at") is None:
                    update_object["updated_at"] = datetime.now(ZoneInfo("UTC"))
                player = next(p for p in players if p.player_id == player_update.player_id)
                await player.set(update_object)

            await bulk_writer.commit()

    @staticmethod
    async def mark_player_for_balance_update(player_id: str):
        player = await Player.find_one(Player.player_id == player_id)
        player.need_balance_update = True
        await player.save()
        return player

    @staticmethod
    async def mark_players_for_balance_update(query: List) -> None:
        await Player.find(*query).set({"need_balance_update": True})

    @staticmethod
    async def add_user_accounts(
        request: AddMultipleUserAccountsRequest,
    ) -> List[AddUserAccountResponse]:
        user_account_responses: List[AddUserAccountResponse] = []

        for user_account in request.accounts:
            register_player_request = RegisterPlayerRequest(
                **user_account.model_dump(exclude_unset=True),
                app_id=request.app_id,
                platform_id=request.platform_id,
            )
            register_player_response = await PlayerRegistrationService().register_player(
                request=register_player_request,
                is_register_request=False,
            )
            user_account_response = AddUserAccountResponse(
                player_id=register_player_response.player_id,
                platform_id=register_player_response.platform_id,
                account=register_player_response.account,
                external_ip=register_player_response.external_ip,
                error=register_player_response.error,
                exception=register_player_response.exception,
                country_code=user_account.country_code,
                phone_number=user_account.phone_number,
            )

            user_account_responses.append(user_account_response)

        return user_account_responses

    @staticmethod
    def convert_currency(
        amount: float | int, from_currency: CurrencyType, to_currency: CurrencyType
    ):
        if from_currency == to_currency:
            return amount

        if from_currency == CurrencyType.USD and to_currency == CurrencyType.GOLD:
            return amount * USD_GOLD_CONVERSION_RATE
        if from_currency == CurrencyType.GOLD and to_currency == CurrencyType.USD:
            return amount / USD_GOLD_CONVERSION_RATE

        raise ValueError(f"You probably shouldn't convert from {from_currency} to {to_currency}")

    @staticmethod
    def build_allowed_players_query(app_id: AppType, play_time: time) -> list:
        return [
            Player.status == PlayerStatus.IDLE.value,
            Player.enabled == True,  # noqa: E712
            Or(
                # hacky subquery to filter some players to check by play_time_range, because Pymongo does not support "$expr" to compare fields internally
                Player.play_time_range.start < play_time,
                Player.play_time_range.end > play_time,
            ),
            Or(
                In(Player.allowed_games, [app_id]),
                Player.app_id == app_id,
            ),
        ]

    @staticmethod
    def build_sufficient_balance_players_for_game_query(
        query: List,
        currency: CurrencyType,
        fee: float,
        allowed_ticket_ids: list[int] = None,
    ) -> list:
        # Build balance conditions based on currency
        balance_conditions = []

        match currency:
            case CurrencyType.USD:
                gold_fee = PlayerService.convert_currency(fee, CurrencyType.USD, CurrencyType.GOLD)
                balance_conditions = [
                    Player.balance.gold >= gold_fee,
                    Player.balance.usd >= fee,
                ]
            case CurrencyType.GOLD:
                usd_fee = PlayerService.convert_currency(fee, CurrencyType.GOLD, CurrencyType.USD)
                balance_conditions = [
                    Player.balance.gold >= fee,
                    Player.balance.usd >= usd_fee,
                ]
            case CurrencyType.DIAMOND:
                balance_conditions = [Player.balance.diamond >= fee]
            case _:
                raise ValueError(
                    f"Unsupported currency: {currency}. Expected one of ['USD', 'GOLD', 'DIAMOND']"
                )

        # Build the final condition: balance OR valid tickets
        final_conditions = balance_conditions.copy()

        # Add ticket condition if allowed_ticket_ids are provided
        if allowed_ticket_ids:
            final_conditions.append(In(Player.balance.tickets, allowed_ticket_ids))

        # Add the OR condition to the query
        query.append(Or(*balance_conditions))

        return query

    @staticmethod
    async def find_players_for_tournament(
        tournament_app_id: int,
        play_time: time,
        currency: CurrencyType,
        fee: float,
        sign_up_options: str = None,
        exclude_player_ids: list[str] = None,
    ) -> list[Player]:
        """
        Find players for tournament, prioritizing those with matching tickets.
        """

        query = PlayerService.build_allowed_players_query(tournament_app_id, play_time)

        allowed_ticket_ids = PlayerService.extract_ticket_ids(sign_up_options)

        players_for_game_query = PlayerService.build_sufficient_balance_players_for_game_query(
            query, currency, fee, allowed_ticket_ids
        )

        if exclude_player_ids:
            players_for_game_query.append(NotIn(Player.player_id, exclude_player_ids))

        players = await Player.find(*players_for_game_query).to_list()
        players = sorted(
            players,
            key=lambda p: bool(set(p.balance.tickets) & set(allowed_ticket_ids)),
            reverse=True,
        )

        logger.debug(
            "PlayerService.find_players_for_tournament",
            f"Found {len(players)} players with matching tickets or sufficient balance",
        )

        return players

    @staticmethod
    def extract_ticket_ids(sign_up_options: str) -> list[int]:
        """
        Extract ticket IDs from signUpOptions comma-separated string.
        Example: "gold,tool,specific:mtt:a92:1754,a92:1593" -> [1754, 1593]
        """
        if not sign_up_options:
            return []

        import re

        ticket_ids = []
        # Find all numbers that come after a colon
        matches = re.findall(r":(\d+)", sign_up_options)

        for match in matches:
            try:
                ticket_ids.append(int(match))
            except ValueError:
                continue

        # Also find standalone numbers, but only if there are multiple comma-separated parts
        parts = sign_up_options.split(",")
        if len(parts) > 1:  # Only process standalone numbers if there are multiple parts
            for part in parts:
                part = part.strip()
                if part.isdigit():
                    ticket_ids.append(int(part))

        return ticket_ids

    @staticmethod
    def find_matching_ticket_id(player_tickets: list, allowed_ticket_ids: list[int]) -> int | None:
        if not player_tickets or not allowed_ticket_ids:
            return None

        # Convert allowed_ticket_ids to a set for faster lookup
        allowed_set = set(allowed_ticket_ids)

        for ticket in player_tickets:
            if isinstance(ticket, int) and ticket in allowed_set:
                return ticket
            elif isinstance(ticket, str):
                try:
                    ticket_int = int(ticket)
                    if ticket_int in allowed_set:
                        return ticket_int
                except ValueError:
                    continue

        return None

    @staticmethod
    async def consume_ticket(player: Player, ticket_id: int) -> None:
        """
        Remove a ticket from player's balance if it exists.
        """
        if not player.balance or not player.balance.tickets:
            return

        if ticket_id in player.balance.tickets:
            player.balance.tickets.remove(ticket_id)
            await player.save()

    @staticmethod
    async def update_avatar(player_id: str, avatar_url: str) -> Player:
        player = await Player.find_one(Player.player_id == player_id)
        player.avatar_url = avatar_url
        player.avatar_changed = True
        await player.save()
        return player
